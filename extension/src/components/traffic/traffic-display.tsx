import React, { useState, useEffect } from 'react'
import { Card, CardContent } from '~/components/ui/card'
import { Button } from '~/components/ui/button'
import { Skeleton } from '~/components/ui/skeleton'
import type { SimilarWebData } from '~/types'
import { similarWebService } from '~/services/traffic'

interface TrafficDisplayProps {
  domain?: string
  showRefreshButton?: boolean
  className?: string
  refreshTrigger?: number // 用于触发刷新的属性
}

export function TrafficDisplay({ 
  domain, 
  showRefreshButton = true,
  className = '',
  refreshTrigger
}: TrafficDisplayProps) {
  const [trafficData, setTrafficData] = useState<SimilarWebData | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (domain) {
      fetchTrafficData(domain)
    } else {
      fetchCurrentPageTraffic()
    }
  }, [domain])

  // 监听刷新触发器
  useEffect(() => {
    if (refreshTrigger && refreshTrigger > 0) {
      if (domain) {
        fetchTrafficData(domain)
      } else {
        fetchCurrentPageTraffic()
      }
    }
  }, [refreshTrigger, domain])

  const fetchTrafficData = async (targetDomain: string) => {
    setLoading(true)
    setError(null)
    
    try {
      console.log(`开始获取域名 ${targetDomain} 的流量数据`)
      
      // 只获取真实的API数据
      const data = await similarWebService.getWebsiteTraffic(targetDomain)
      
      if (!data) {
        console.log('API数据获取失败')
        setError('无法获取流量数据 - API暂时无法访问')
        setTrafficData(null)
      } else {
        console.log('成功获取API数据:', data)
        // 检查数据完整性
        const hasBasicData = data.traffic.totalVisits > 0 || 
                           data.traffic.uniqueVisitors > 0 || 
                           data.traffic.pageViews > 0
        
        if (!hasBasicData) {
          console.log('API数据不完整')
          setError('获取的数据不完整')
        }
        
        setTrafficData(data)
      }
    } catch (err) {
      console.error('获取流量数据时发生错误:', err)
      setError('获取流量数据失败')
      setTrafficData(null)
    } finally {
      setLoading(false)
    }
  }

  const fetchCurrentPageTraffic = async () => {
    setLoading(true)
    setError(null)
    
    try {
      console.log('开始获取当前页面流量数据')
      const data = await similarWebService.getCurrentPageTraffic()
      
      if (!data) {
        console.log('当前页面API数据获取失败')
        // 获取当前页面域名用于错误提示
        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true })
        if (tab?.url) {
          const currentDomain = new URL(tab.url).hostname.replace(/^www\./, '')
          setError(`无法获取 ${currentDomain} 的流量数据`)
        } else {
          setError('无法获取当前页面信息')
        }
        setTrafficData(null)
      } else {
        console.log('成功获取当前页面数据:', data)
        setTrafficData(data)
      }
    } catch (err) {
      console.error('获取当前页面流量数据时发生错误:', err)
      setError('获取当前页面流量数据失败')
      setTrafficData(null)
    } finally {
      setLoading(false)
    }
  }

  const handleRefresh = () => {
    if (domain) {
      fetchTrafficData(domain)
    } else {
      fetchCurrentPageTraffic()
    }
  }

  const formatNumber = (num: number): string => {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`
    }
    if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`
    }
    return num.toString()
  }

  const formatDuration = (seconds: number): string => {
    if (seconds >= 60) {
      const minutes = Math.floor(seconds / 60)
      const remainingSeconds = Math.floor(seconds % 60)
      return `${minutes}分${remainingSeconds}秒`
    }
    return `${Math.floor(seconds)}秒`
  }

  const formatPercentage = (value: number): string => {
    return `${(value * 100).toFixed(1)}%`
  }

  if (loading) {
    return (
      <Card className={className}>
        <CardContent className="py-4">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Skeleton className="h-5 w-24" />
              {showRefreshButton && <Skeleton className="h-8 w-8 rounded" />}
            </div>
            <div className="grid grid-cols-2 gap-4">
              {Array.from({ length: 6 }).map((_, i) => (
                <div key={i} className="space-y-2">
                  <Skeleton className="h-4 w-16" />
                  <Skeleton className="h-6 w-20" />
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error && !trafficData) {
    return (
      <Card className={className}>
        <CardContent className="py-4">
          <div className="text-center space-y-3">
            <div className="flex items-center justify-center space-x-2">
              <svg className="w-5 h-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <div className="text-sm text-red-600">{error}</div>
            </div>
            <div className="text-xs text-muted-foreground">
              请检查网络连接或稍后重试
            </div>
            {showRefreshButton && (
              <Button 
                variant="outline" 
                size="sm" 
                onClick={handleRefresh}
                className="h-8"
              >
                重试
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    )
  }

  if (!trafficData) {
    return (
      <Card className={className}>
        <CardContent className="py-4">
          <div className="text-center space-y-3">
            <div className="flex items-center justify-center space-x-2">
              <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
              <div className="text-sm text-muted-foreground">暂无流量数据</div>
            </div>
            <div className="text-xs text-muted-foreground">
              请检查域名是否正确或稍后重试
            </div>
            {showRefreshButton && (
              <Button 
                variant="outline" 
                size="sm" 
                onClick={handleRefresh}
                className="h-8"
              >
                重试
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={className}>
      <CardContent className="py-4">
        <div className="space-y-4">
          {/* 标题栏 */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <h3 className="font-medium text-sm">网站流量分析</h3>
              {error && (
                <div className="px-2 py-1 text-xs bg-red-100 text-red-800 rounded-md">
                  {error}
                </div>
              )}
            </div>
            {showRefreshButton && (
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={handleRefresh}
                className="h-8 w-8 p-0"
                disabled={loading}
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
              </Button>
            )}
          </div>

          {/* 域名显示 */}
          <div className="text-xs text-muted-foreground">
            {trafficData.domain}
            {trafficData.globalRank && (
              <span className="ml-2">
                全球排名: #{trafficData.globalRank.toLocaleString()}
              </span>
            )}
          </div>

          {/* 流量概览 */}
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="space-y-1">
              <div className="text-muted-foreground">总访问量</div>
              <div className="font-semibold text-lg">
                {formatNumber(trafficData.traffic.totalVisits)}
              </div>
            </div>
            <div className="space-y-1">
              <div className="text-muted-foreground">独立访客</div>
              <div className="font-semibold text-lg">
                {formatNumber(trafficData.traffic.uniqueVisitors)}
              </div>
            </div>
            <div className="space-y-1">
              <div className="text-muted-foreground">跳出率</div>
              <div className="font-semibold">
                {(trafficData.traffic.bounceRate * 100).toFixed(1)}%
              </div>
            </div>
            <div className="space-y-1">
              <div className="text-muted-foreground">平均时长</div>
              <div className="font-semibold">
                {formatDuration(trafficData.traffic.averageVisitDuration)}
              </div>
            </div>
            <div className="space-y-1">
              <div className="text-muted-foreground">页面/访问</div>
              <div className="font-semibold">
                {trafficData.traffic.pagesPerVisit.toFixed(1)}
              </div>
            </div>
            <div className="space-y-1">
              <div className="text-muted-foreground">页面浏览</div>
              <div className="font-semibold">
                {formatNumber(trafficData.traffic.pageViews)}
              </div>
            </div>
          </div>

          {/* 国家流量分布 */}
          {trafficData.topCountries.length > 0 && (
            <div className="space-y-2">
              <h4 className="font-medium text-sm">主要国家流量</h4>
              <div className="space-y-2">
                {trafficData.topCountries.slice(0, 3).map((country, index) => (
                  <div key={country.countryCode} className="flex items-center justify-between text-sm">
                    <span className="flex items-center gap-2">
                      <span className="text-xs text-muted-foreground">#{index + 1}</span>
                      <span>{country.countryName}</span>
                    </span>
                    <div className="flex items-center gap-2">
                      <span className="text-xs text-muted-foreground">
                        {formatNumber(country.visits)}
                      </span>
                      <span className="font-medium">
                        {formatPercentage(country.trafficShare)}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* 热门关键词 */}
          {trafficData.topKeywords.length > 0 && (
            <div className="space-y-2">
              <h4 className="font-medium text-sm">热门关键词</h4>
              <div className="space-y-1">
                {trafficData.topKeywords.slice(0, 5).map((keyword, index) => (
                  <div key={keyword.keyword} className="flex items-center justify-between text-sm">
                    <span className="flex items-center gap-2">
                      <span className="text-xs text-muted-foreground">#{index + 1}</span>
                      <span className="truncate">{keyword.keyword}</span>
                    </span>
                    <span className="font-medium text-xs">
                      {formatPercentage(keyword.trafficShare)}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* 更新时间 */}
          <div className="text-xs text-muted-foreground">
            更新时间: {trafficData.traffic.lastUpdated.toLocaleString()}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default TrafficDisplay